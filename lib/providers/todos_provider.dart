import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:focus_todo2/data/todo_repository.dart';
import 'package:focus_todo2/models/task_priority.dart';
import 'package:focus_todo2/models/todo.dart';
import 'package:uuid/uuid.dart';

final todoRepositoryProvider = Provider<TodoRepository>(
  (ref) => throw UnimplementedError(),
);

final todosProvider = StateNotifierProvider<TodosNotifier, List<Todo>>((ref) {
  final repository = ref.watch(todoRepositoryProvider);
  return TodosNotifier(repository, ref);
});

final todoProvider = Provider.family<Todo?, String>((ref, todoId) {
  final todos = ref.watch(todosProvider);
  try {
    return todos.firstWhere((todo) => todo.id == todoId);
  } catch (e) {
    return null;
  }
});

class TodosNotifier extends StateNotifier<List<Todo>> {
  final TodoRepository _repository;
  final Ref ref;

  TodosNotifier(this._repository, this.ref) : super([]) {
    _loadTodos();
  }

  Future<void> _loadTodos() async {
    state = await _repository.getTodos();
  }

  Future<void> addTodo(
    String title,
    String description, 
    String? categoryId,{
      TaskPriority priority = TaskPriority.none,
      DateTime? dueDate,
      DateTime? reminderDateTime,
      bool isRecurring = false,
    }
  ) async {
    final newTodo = Todo(
        id: const Uuid().v4(),
        title: title,
        description: description,
        priority: priority,
        categoryId: categoryId,
        createdAt: DateTime.now(),
        dueDate: dueDate,
        reminderDateTime: reminderDateTime,
        isRecurring: isRecurring,
        orderIndex: DateTime.now().millisecondsSinceEpoch,
      );
    print('Before add - State length: ${state.length}');
    await _repository.addTodo(newTodo);
    state = [...state, newTodo];
    print('After add - State length: ${state.length}');
    print('Added todo: ${newTodo.title}');
  }

  Future<void> toggleTodo(String id) async {
    final index = state.indexWhere((todo) => todo.id == id);
    if (index != -1) {
      final todo = state[index];
      Todo updatedTodo;

      if(!todo.isCompleted){
        updatedTodo = todo.copyWith(isCompleted: true);
      } else {
        updatedTodo = todo.copyWith(isCompleted: false);
      }

      await _repository.updateTodo(updatedTodo);
      state = [
        for (final t in state) (t.id == id ? updatedTodo : t)];
    }
  }


}
