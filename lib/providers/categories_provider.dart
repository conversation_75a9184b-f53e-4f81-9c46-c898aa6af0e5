import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:focus_todo2/models/task_category.dart';
import 'package:hive_flutter/hive_flutter.dart';

final categoriesProvider =
    StateNotifierProvider<CategoryNotifier, List<TaskCategory>>(
      (ref) => CategoryNotifier(),
    );

class CategoryNotifier extends StateNotifier<List<TaskCategory>> {
  CategoryNotifier() : super([]) {
    _loadCategories();
  }
  final _categoryBox = Hive.box<TaskCategory>('categories');

  Future<void> _loadCategories() async {
    state = _categoryBox.values.toList();
  }

  Future<List<TaskCategory>> getCategories() async {
    return _categoryBox.values.toList();
  }

  Future<void> addCategory(TaskCategory category) async {
    await _categoryBox.add(category);
    state = [..._categoryBox.values.toList()];
  }

  Future<void> updateCategory(TaskCategory updatedCategory) async {
    final index = state.indexWhere((cat) => cat.id == updatedCategory.id);
    if (index != -1) {
      await _categoryBox.putAt(index, updatedCategory);
      state = [..._categoryBox.values.toList()];
    }
  }
}
