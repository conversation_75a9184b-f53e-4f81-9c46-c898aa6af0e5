import 'package:focus_todo2/models/task_category.dart';
import 'package:focus_todo2/models/task_priority.dart';
import 'package:focus_todo2/models/todo.dart';
import 'package:hive_flutter/hive_flutter.dart';

class HiveServices {
  static Future<void> init() async {
    await Hive.initFlutter();

    Hive.registerAdapter(TodoAdapter());
    Hive.registerAdapter(TaskPriorityAdapter());
    Hive.registerAdapter(TaskCategoryAdapter());

    await Hive.openBox<Todo>('todos');
    await Hive.openBox<TaskCategory>('categories');
    await Hive.openBox('settings');
  }

  /// Clear all Hive data - useful for fixing corruption issues
  static Future<void> clearAllData() async {
    try {
      await Hive.box<Todo>('todos').clear();
      await Hive.box<TaskCategory>('categories').clear();
      await Hive.box('settings').clear();
      // ignore: avoid_print
      print('All Hive data cleared successfully');
    } catch (e) {
      // ignore: avoid_print
      print('Error clearing Hive data: $e');
    }
  }
}
