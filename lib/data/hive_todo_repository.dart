import 'package:focus_todo2/data/todo_repository.dart';
import 'package:focus_todo2/models/todo.dart';
import 'package:hive_flutter/hive_flutter.dart';

class HiveTodoRepository implements TodoRepository {
  final Box<Todo> _todoBox = Hive.box<Todo>('todos');

  @override
  Future<List<Todo>> getTodos() async {
    try {
      // Filter out any corrupted data that's not a Todo object
      final todos = <Todo>[];
      for (final value in _todoBox.values) {
        // Explicitly check if the value is a Todo to handle corrupted data
        // ignore: unnecessary_type_check
        if (value is Todo) {
          todos.add(value);
        } else {
          // ignore: avoid_print
          print(
            'Warning: Found non-Todo object in todos box: ${value.runtimeType}',
          );
        }
      }
      return todos;
    } catch (e) {
      // ignore: avoid_print
      print('Error loading todos: $e');
      // If there's a corruption issue, clear the box and return empty list
      await _clearCorruptedData();
      return [];
    }
  }

  @override
  Future<void> addTodo(Todo todo) async {
    try {
      await _todoBox.put(todo.id, todo); // <PERSON><PERSON><PERSON> put dengan id
      print('Todo saved to Hive: ${todo.title}'); // Debug
    } catch (e) {
      print('Error saving todo: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateTodo(Todo todo) async {
    try {
      await _todoBox.put(todo.id, todo);
    } catch (e) {
      print('Error updating todo: $e');
      rethrow;
    }
  }

  Future<void> _clearCorruptedData() async {
    try {
      print('Clearing corrupted data from todos box...');
      await _todoBox.clear();
      print('Todos box cleared successfully');
    } catch (e) {
      print('Error clearing todos box: $e');
    }
  }
}
