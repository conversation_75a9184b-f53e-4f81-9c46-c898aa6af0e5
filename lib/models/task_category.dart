import 'package:hive_flutter/hive_flutter.dart';

part 'task_category.g.dart';

@HiveType(typeId: 2)
class TaskCategory extends HiveObject {

  @HiveField(0)
  final String id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final int colorValue;

  TaskCategory({
    required this.id,
    required this.name,
    required this.colorValue,
  });

  TaskCategory copyWith({
    String? id,
    String? name,
    int? colorValue,
  }){
    return TaskCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      colorValue: colorValue ?? this.colorValue,
    );
  }
}