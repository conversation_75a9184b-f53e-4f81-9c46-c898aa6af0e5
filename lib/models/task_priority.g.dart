// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task_priority.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class TaskPriorityAdapter extends TypeAdapter<TaskPriority> {
  @override
  final int typeId = 1;

  @override
  TaskPriority read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return TaskPriority.none;
      case 1:
        return TaskPriority.low;
      case 2:
        return TaskPriority.medium;
      case 3:
        return TaskPriority.high;
      case 4:
        return TaskPriority.veryHigh;
      default:
        return TaskPriority.none;
    }
  }

  @override
  void write(BinaryWriter writer, TaskPriority obj) {
    switch (obj) {
      case TaskPriority.none:
        writer.writeByte(0);
        break;
      case TaskPriority.low:
        writer.writeByte(1);
        break;
      case TaskPriority.medium:
        writer.writeByte(2);
        break;
      case TaskPriority.high:
        writer.writeByte(3);
        break;
      case TaskPriority.veryHigh:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TaskPriorityAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
