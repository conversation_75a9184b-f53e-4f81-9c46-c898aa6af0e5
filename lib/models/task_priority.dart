import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter/material.dart';

part 'task_priority.g.dart';

@HiveType(typeId: 1)
enum TaskPriority {
  @HiveField(0) none,
  @HiveField(1) low,
  @HiveField(2) medium,
  @HiveField(3) high,
  @HiveField(4) veryHigh;

  Color getColor() {
    switch(this){
      case TaskPriority.none:
        return Colors.grey;
      case TaskPriority.low:
        return Colors.green;
      case TaskPriority.medium:
        return Colors.yellow;
      case TaskPriority.high:
        return Colors.orange;
      case TaskPriority.veryHigh:
        return Colors.red;
    }
  }

  String getLabel(){
    switch(this){
      case TaskPriority.none:
        return 'None';
      case TaskPriority.low:
        return 'Low';
      case TaskPriority.medium:
        return 'Medium';
      case TaskPriority.high:
        return 'High';
      case TaskPriority.veryHigh:
        return 'Very High';
    }
  }
  IconData getPriorityIcon(){
    return Icons.flag;
    }
}