import 'package:flutter/foundation.dart';
import 'package:focus_todo2/models/task_priority.dart';
import 'package:hive_flutter/hive_flutter.dart';

part 'todo.g.dart';

@immutable
@HiveType(typeId: 0)
class Todo {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String title;
  
  @HiveField(2)
  final String description;

  @HiveField(3)
  final TaskPriority priority;

  @HiveField(4)
  final bool isCompleted;

  @HiveField(5)
  final DateTime? completedAt;

  @HiveField(6)
  final DateTime? createdAt;

  @HiveField(7)
  final String? categoryId;

  @HiveField(8)
  final DateTime? dueDate;

  @HiveField(9)
  final int orderIndex;

  @HiveField(10)
  final DateTime? reminderDateTime;

  @HiveField(11)
  final bool isRecurring;

  const Todo({
    required this.id,
    required this.title,
    this.description = '',
    this.priority = TaskPriority.low,
    this.isCompleted = false,
    required this.createdAt,
    this.completedAt,
    this.categoryId,
    this.dueDate,
    this.orderIndex = 0,
    this.reminderDateTime,
    this.isRecurring = false,
  });

  Future<void> save() async{
    await Hive.box<Todo>('todos').put(id, this);
  }

  Future<void> delete() async{
    await Hive.box<Todo>('todos').delete(id);
  }

  Todo copyWith({
    String? id,
    String? title,
    String? description,
    TaskPriority? priority,
    bool? isCompleted,
    DateTime? completedAt,
    DateTime? createdAt,
    String? categoryId,
    DateTime? dueDate,
    int? orderIndex,
    DateTime? reminderDateTime,
    bool? isRecurring,
  }) {
    return Todo(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      priority: priority ?? this.priority,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
      createdAt: createdAt ?? this.createdAt,
      categoryId: categoryId ?? this.categoryId,
      dueDate: dueDate ?? this.dueDate,
      orderIndex: orderIndex ?? this.orderIndex,
      reminderDateTime: reminderDateTime ?? this.reminderDateTime,
      isRecurring: isRecurring ?? this.isRecurring,
    );
  }
}