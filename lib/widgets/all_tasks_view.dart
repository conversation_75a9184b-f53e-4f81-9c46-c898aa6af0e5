import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:focus_todo2/providers/todos_provider.dart';
import 'package:focus_todo2/utils/screen_config.dart';
import 'package:focus_todo2/widgets/task_list_display.dart';

class AllTaskView extends ConsumerStatefulWidget {
  const AllTaskView({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _AllTaskViewState();
}

class _AllTaskViewState extends ConsumerState<AllTaskView> {
  bool _showCompleted = false;

  @override
  Widget build(BuildContext context) {
    final todos = ref.watch(todosProvider);
    final incompletedTodos = todos.where((todo) => !todo.isCompleted).toList();
    final completedTodos = todos.where((todo) => todo.isCompleted).toList();
    final screenConfig = ScreenConfig(context);

    return TaskListDisplay(screenConfig: screenConfig,
      incompletedTodos: incompletedTodos,
      completedTodos: completedTodos,
      showCompleted: _showCompleted,
      onToggleCompleted: () {
        setState(() {
          _showCompleted = !_showCompleted;
        });
      },
    );
  }
}