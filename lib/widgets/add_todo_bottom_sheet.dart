import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:focus_todo2/models/task_priority.dart';
import 'package:focus_todo2/models/todo.dart';
import 'package:focus_todo2/providers/todos_provider.dart';
import 'package:uuid/uuid.dart';

class AddTodoBottomSheet extends ConsumerStatefulWidget {
  final String? categoryId;

  const AddTodoBottomSheet({super.key, this.categoryId});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _AddTodoBottomSheetState();
}

class _AddTodoBottomSheetState extends ConsumerState<AddTodoBottomSheet> {
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  bool _showMoreOptions = false;
  TaskPriority _selectedPriority = TaskPriority.none;
  DateTime? _selectedDueDate;
  DateTime? _selectedReminderDateTime;
  bool _isRecurring = false;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(
        16,
        16,
        16,
        MediaQuery.of(context).viewInsets.bottom + 16,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _titleController,
            decoration: const InputDecoration(labelText: 'Title'),
            autofocus: true,
            textCapitalization: TextCapitalization.sentences,
            onSubmitted: (value) => _addTodo(context),
          ),
          if (_showMoreOptions) ...[
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(labelText: 'Description'),
              textCapitalization: TextCapitalization.sentences,
            ),
            const SizedBox(height: 14),
            DropdownButtonFormField<TaskPriority>(
              value: _selectedPriority,
              items:
                  TaskPriority.values.map((priority) => DropdownMenuItem<TaskPriority>(
                        value: priority,
                        child: Row(
                          children: [
                            Icon(
                              priority.getPriorityIcon(),
                              color: priority.getColor(),
                            ),
                            const SizedBox(width: 8),
                            Text(priority.getLabel()),
                          ],
                        ),
                      )).toList(),
              decoration: const InputDecoration(labelText: 'Priority'),
              onChanged: (TaskPriority? newValue) {
                if (newValue != null) {
                  setState(() {
                    _selectedPriority = newValue;
                  });
                }}),
            const SizedBox(height: 8),
            // --- Due date ---
            ListTile(
              leading: const Icon(Icons.calendar_today_rounded),
              title: Text(
                _selectedDueDate == null ? 'Due Date' : _selectedDueDate.toString(),
              ),
              trailing: _selectedDueDate == null
                  ? const Icon(Icons.keyboard_arrow_right)
                  : IconButton(
                icon: const Icon(Icons.close),
                onPressed: () {
                  setState(() {
                    _selectedDueDate = null;
                  });
                },
              ),
              onTap: () async {
                final selectedDate = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                if (selectedDate != null) {
                  setState(() {
                    _selectedDueDate = selectedDate;
                  });
                }
                }
            ),
            const SizedBox(height: 8),
      
                
          ],
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                onPressed: () {
                  setState(() {
                    _showMoreOptions = !_showMoreOptions;
                  });
                },
                child: Icon(
                  _showMoreOptions
                      ? Icons.keyboard_arrow_up
                      : Icons.keyboard_arrow_down,
                ),
              ),
              ElevatedButton(
                onPressed: () => _addTodo(context),
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                child: const Text('+'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _addTodo(BuildContext context) {
    final title = _titleController.text.trim();
    final description = _descriptionController.text.trim();

    if (title.isNotEmpty) {
      // Buat objek Todo lengkap
      
      ref.read(todosProvider.notifier).addTodo(
        title,
        description,
        widget.categoryId,
        priority: _selectedPriority,
        dueDate: _selectedDueDate,
        reminderDateTime: _selectedReminderDateTime,
        isRecurring: _isRecurring,
      );
      Navigator.of(context).pop();
    }
  }
}
