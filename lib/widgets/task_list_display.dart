import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:focus_todo2/models/todo.dart';
import 'package:focus_todo2/utils/screen_config.dart';
import 'package:focus_todo2/widgets/task_item.dart';

class TaskListDisplay extends ConsumerWidget {
  final List<Todo> incompletedTodos;
  final List<Todo> completedTodos;
  final ScreenConfig screenConfig;
  final bool showCompleted;
  final VoidCallback onToggleCompleted;

  const TaskListDisplay({
    super.key,
    required this.incompletedTodos,
    required this.completedTodos,
    required this.screenConfig,
    required this.showCompleted,
    required this.onToggleCompleted,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      child: Column(
        children: [
          // Incomplete task section
          Container(
            margin: EdgeInsets.fromLTRB(
              screenConfig.containerTaskHorizontalMargin,
              screenConfig.containerTaskVerticalMargin,
              screenConfig.containerTaskHorizontalMargin,
              0,
            ),
            padding: EdgeInsets.all(screenConfig.containerTaskPadding),
            decoration: BoxDecoration(
              color: const Color.fromARGB(255, 104, 104, 104),
              borderRadius: BorderRadius.circular(15),
            ),
            child: AnimatedSize(
              alignment: Alignment.topCenter,
              duration: const Duration(milliseconds: 370),
              curve: Curves.easeInOut,
              child:
                  incompletedTodos.isEmpty
                      ? Container(
                        padding: const EdgeInsets.symmetric(vertical: 20),
                        alignment: Alignment.center,
                        child: const Text(
                          'No tasks to show',
                          style: TextStyle(fontSize: 18, color: Colors.grey),
                          textAlign: TextAlign.center,
                        ),
                      )
                      : ReorderableListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: incompletedTodos.length,
                        itemBuilder: (context, index) {
                          final todo = incompletedTodos[index];
                          // Key dipindahkan ke TaskItem agar ReorderableListView berfungsi
                          return TaskItem(
                            key: ValueKey(todo.id),
                            todoId: todo.id,
                          );
                        },
                        onReorder: (oldIndex, newIndex) {
                          // Handle reordering logic here
                        },
                      ),
            ),
          ),
          if (incompletedTodos.isNotEmpty || completedTodos.isNotEmpty)
            Container(
              margin: EdgeInsets.fromLTRB(
                screenConfig.containerTaskHorizontalMargin,
                screenConfig.containerTaskVerticalMargin,
                screenConfig.containerTaskHorizontalMargin,
                screenConfig.containerTaskVerticalMargin,
              ),
              padding: EdgeInsets.all(screenConfig.containerTaskPadding),
              decoration: BoxDecoration(
                color: const Color.fromARGB(255, 104, 104, 104),
                borderRadius: BorderRadius.circular(15),
              ),
              child: AnimatedSize(
                alignment: Alignment.bottomCenter,
                duration: const Duration(milliseconds: 370),
                curve: Curves.easeInOut,
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          const Text(
                            'Completed Tasks',
                            style: TextStyle(fontSize: 18, color: Colors.white),
                          ),
                          IconButton(
                            icon: Icon(
                              showCompleted
                                  ? Icons.arrow_drop_down
                                  : Icons.arrow_drop_up,
                            ),

                            onPressed: () {
                              onToggleCompleted();
                            },
                          ),
                        ],
                      ),
                    ),
                    if (showCompleted)
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: completedTodos.length,
                        itemBuilder: (context, index) {
                          final todo = completedTodos[index];
                          return GestureDetector(
                            child: TaskItem(todoId: todo.id),
                          );
                        },
                      ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}
