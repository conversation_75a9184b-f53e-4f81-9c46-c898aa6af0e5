import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:focus_todo2/providers/todos_provider.dart';
import 'package:focus_todo2/screens/todo_detail_screen.dart';

class TaskItem extends ConsumerStatefulWidget {
  final String todoId;
  const TaskItem({super.key, required this.todoId});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _TaskItemState();
}

class _TaskItemState extends ConsumerState<TaskItem>
    with SingleTickerProviderStateMixin {
  bool _isCompleting = false;
  bool _isCompletedLocal = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _fadeAnimation = Tween<double>(begin: 1.0, end: 0.1).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _animationController.reverse();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final todo = ref.watch(todoProvider(widget.todoId));
    final isCompleted = ref.watch(
      todoProvider(widget.todoId).select((todo) => todo?.isCompleted ?? false),
    );

    if (todo == null) {
      return ListTile(title: const Text('Todo not found'));
    }

    if (!_isCompleting) {
      _isCompletedLocal = isCompleted;
    }

    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => TodoDetailScreen()),
        );
      },
      child: Table(
        columnWidths: const <int, TableColumnWidth>{
          0: IntrinsicColumnWidth(),
          1: FlexColumnWidth(),
          2: IntrinsicColumnWidth(),
        },
        defaultVerticalAlignment: TableCellVerticalAlignment.middle,
        children: <TableRow>[
          TableRow(
            children: <Widget>[
              // Checkbox
              TableCell(
                child: Padding(
                  padding: const EdgeInsets.only(right: 4.0),
                  child: ScaleTransition(
                    scale: _scaleAnimation,
                    child: FadeTransition(
                      opacity: _fadeAnimation,
                      child: Checkbox(
                        visualDensity: VisualDensity.compact,
                        value: _isCompletedLocal,
                        onChanged: (value) async {
                          _animationController.forward();
                          setState(() {
                            _isCompleting = true;
                            _isCompletedLocal = value!;
                          });
                          await Future.delayed(
                            const Duration(milliseconds: 300),
                          );
                          await ref.read(todosProvider.notifier).toggleTodo(todo.id);
                          if (mounted) {
                            setState(() {
                              _isCompleting = false;
                            });
                          }
                        },
                      ),
                    ),
                  ),
                ),
              ),
              // Title
              TableCell(
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Text(
                    todo.title,
                    style: TextStyle(
                      decoration: _isCompletedLocal
                          ? TextDecoration.lineThrough
                          : TextDecoration.none,
                    ),
                  ),
                ),
              ),
              // Priority icon (third column)
              TableCell(
                child: Icon(
                  todo.priority.getPriorityIcon(),
                  color: todo.priority.getColor(),
                  size: 16,
                ),
              ),
            ],
          ),
          TableRow(
            children: <Widget>[
              const TableCell(child: SizedBox.shrink()),
              TableCell(
                child: Text(todo.description),
              ),
              const TableCell(child: SizedBox.shrink()),
            ],
          ),
        ],
      ),
    );
  }
}
