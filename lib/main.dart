import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:focus_todo2/data/hive_services.dart';
import 'package:focus_todo2/data/hive_todo_repository.dart';
import 'package:focus_todo2/providers/todos_provider.dart';
import 'package:focus_todo2/screens/app_shell.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Inisialisasi Hive
  await HiveServices.init();

  final repository = HiveTodoRepository();
  // Untuk menggunakan Riverpod, Anda perlu membungkus seluruh aplikasi
  // dalam widget `ProviderScope`.
  runApp(
    ProviderScope(
      overrides: [todoRepositoryProvider.overrideWithValue(repository)],
      child: MyApp(),
    ),
  );
}

class MyApp extends ConsumerStatefulWidget {
  const MyApp({super.key});

  @override
  ConsumerState<MyApp> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: const AppShell(),
      debugShowCheckedModeBanner: false,
    );
  }
}
