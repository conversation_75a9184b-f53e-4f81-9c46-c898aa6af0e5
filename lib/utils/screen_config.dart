import 'package:flutter/material.dart';

class ScreenConfig {
  final BuildContext context;

  final double containerTaskHorizontalMargin;
  final double containerTaskVerticalMargin;

  final double containerTaskPadding;

  //AllTaskView
  final double allTaskViewHorizontalPadding;

  ScreenConfig(this.context) :

    // Container Task
    containerTaskHorizontalMargin = MediaQuery.of(context).size.width * 0.05,
    containerTaskVerticalMargin = MediaQuery.of(context).size.height * 0.01,
    containerTaskPadding = MediaQuery.of(context).size.width * 0.03,
  
    // AllTaskView
    allTaskViewHorizontalPadding = MediaQuery.of(context).size.width * 0.03;
}


