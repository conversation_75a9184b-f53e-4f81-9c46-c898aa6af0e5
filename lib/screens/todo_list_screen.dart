import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:focus_todo2/widgets/all_tasks_view.dart';

class TodoListScreen extends ConsumerStatefulWidget{
  const TodoListScreen({super.key});


  @override
  ConsumerState<TodoListScreen> createState() => _TodoListScreenState();
}

class _TodoListScreenState extends ConsumerState<TodoListScreen> {
  @override
  Widget build(BuildContext context) {
    return AllTaskView();
  }

}

