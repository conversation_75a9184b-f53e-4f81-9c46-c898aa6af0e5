import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:focus_todo2/models/task_category.dart';
import 'package:focus_todo2/providers/categories_provider.dart';
import 'package:focus_todo2/providers/todos_provider.dart';
import 'package:focus_todo2/screens/todo_list_screen.dart';
import 'package:focus_todo2/widgets/add_todo_bottom_sheet.dart';
import 'package:hive_flutter/hive_flutter.dart';

class AppShell extends ConsumerStatefulWidget {
  const AppShell({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _AppShellState();
}

class _AppShellState extends ConsumerState<AppShell> {
  int _selectedIndex = 0;
  TabController? _todoListTabController;
  int _initialTodoListTabIndex = 0;

  @override
  void initState() {
    super.initState();
    _initialTodoListTabIndex = Hive.box('settings').get(_tabIndexKey, defaultValue: 0) as int;
  }
  @override
  void dispose() {
    _todoListTabController?.dispose();
    super.dispose();
  }

  AppBar _buildTodoListAppBar(BuildContext context, WidgetRef ref, List<TaskCategory> categories){
    return AppBar(
      title: _AppBarTitle(categories: categories),
    );

  }

  Widget _buildTodoListFab(BuildContext context, WidgetRef ref, List<TaskCategory> categories) {
    return FloatingActionButton(
      onPressed: () {
        String? categoryId;
        if (_todoListTabController != null && categories.isNotEmpty) {
          final tabIndex = _todoListTabController!.index;
          if (tabIndex > 0 && tabIndex <= categories.length) {
            categoryId = categories[tabIndex - 1].id;
          }
        }
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          builder: (context) => AddTodoBottomSheet(categoryId: categoryId),
        );
      },
      child: const Icon(Icons.add),
    );
  }

  @override
  Widget build(BuildContext context) {
    final categories = ref.watch(categoriesProvider);

    return Scaffold(
      appBar: _buildTodoListAppBar(context, ref, categories),
      body: TodoListScreen(),
      floatingActionButton: _buildTodoListFab(context, ref, categories),
    );
  }
}

class _AppBarTitle extends ConsumerWidget {
  final List<TaskCategory> categories;
  const _AppBarTitle({required this.categories});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabIndex = ref.watch(tabIndexProvider);
    final todos = ref.watch(todosProvider);
    
    String titleText = 'All Tasks';
    if (tabIndex == 0) {
      final activeCount = todos.where((todo) => !todo.isCompleted).length;
      return Text('All ($activeCount)');
    } else if (tabIndex > 0 && tabIndex <= categories.length) {
      final category = categories[tabIndex - 1];
      final activeCount = todos.where((todo) => todo.categoryId == category.id && !todo.isCompleted).length;
      return Text('${category.name} ($activeCount)');
    } else {
      return const Text('');
    }
  }
}

const String _tabIndexKey = 'lastTabIndex';
final tabIndexProvider = StateProvider<int>((ref) {
  return Hive.box('settings').get(_tabIndexKey, defaultValue: 0) as int;
});