import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class TodoDetailScreen extends ConsumerStatefulWidget {
  const TodoDetailScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _TodoDetailScreenState();
}

class _TodoDetailScreenState extends ConsumerState<TodoDetailScreen> {

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Todo Detail'),
      ),
      body: const Center(
        child: Text('Todo Detail'),
      ),
    );
  }
}